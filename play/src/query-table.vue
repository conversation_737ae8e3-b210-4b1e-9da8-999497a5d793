<template>
  <f-query-table
    ref="table"
    row-key="date"
    :table-columns="tableColumns"
    :table-data="tableData"
    :border="true"
    :show-header="true"
    :form-data="formData"
    :export-exclude="['operate']"
    :column="2"
    :show-operate="true"
    @print="handlePrint"
  >
    <template #table-top>
      <f-form :column="3">
        <f-form-item>
          <f-input v-model="formData.test1" />
        </f-form-item>
        <f-form-item>
          <f-input v-model="formData.test2" />
        </f-form-item>
        <f-form-item>
          <f-input v-model="formData.test3" />
        </f-form-item>
      </f-form>
    </template>
    <template #operate> 按钮区域 </template>
    <f-table-column type="selection" />
    <f-table-column prop="amount" label="Amount" width="150" formatter="amount">
      <f-table-column prop="name" label="Name" width="150">
        <template #default="{ row }">
          <f-input v-model="row.name" />
        </template>
        <template #edit> 编辑状态 </template>
      </f-table-column>
      <f-table-column prop="date" label="date" />
    </f-table-column>
  </f-query-table>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import {
  FForm,
  FFormItem,
  FInput,
  FQueryTable,
  FTableColumn,
} from '@frontend-plus/components'
const formData = reactive({
  test1: '',
  test2: '',
  test3: '',
})
const tableColumns = [
  { prop: 'selection', type: 'selection' },
  { width: '150px', prop: 'amount', label: 'amount' },
  {
    width: '200px',
    prop: 'date',
    label: 'date',
  },
  {
    width: '150px',
    prop: 'name',
    label: 'name',
  },
  {
    width: '350px',
    prop: 'address',
    label: 'address',
  },
]
const handlePrint = (data) => {
  console.log(data)
}
const testFlag = ref(false)

onMounted(() => {
  testFlag.value = true
})

const tableData = reactive({
  data: [
    {
      date: '2022-10-01',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-02',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-03',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-04',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-05',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-06',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-07',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-08',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-09',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-10',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-11',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
    {
      date: '2022-10-12',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      amount: 50,
    },
  ],
  total: 12,
})
</script>
<style lang="scss">
.fake {
  background-color: orange;
}
</style>
