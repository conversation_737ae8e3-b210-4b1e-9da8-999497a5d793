<template>
  <div>
    <!-- <h1>基础拖拽上传</h1> -->
    <f-button @click="addFile">增加一个fileId</f-button>
    <f-button @click="clearFile">手动清空文件</f-button>

    <f-form label-width="120px" :model="model" :column="3">
      <!-- <f-form-item label="附件">
        <f-input v-model="form.textareaVal1" maxlength="30" />
      </f-form-item>
      <f-form-item label="附件">
        <f-input v-model="form.textareaVal2" maxlength="30" />
      </f-form-item>
      <f-form-item label="附件">
        <f-input v-model="form.textareaVal3" maxlength="30" />
      </f-form-item> -->
      <f-form-item label="附件_attm-upload" :employ="3">
        <f-attm-upload
          ref="fileUpload"
          v-model="model.fileIds"
          drag
          :is-remove-delete-link="false"
          :selectable="selectable"
          :bind-biz-fun="bizFun"
          :before-upload="uploadBeforeCheck"
        />
      </f-form-item>
      <f-form-item label="附件_验证局部禁用删除" :employ="3">
        <f-attm-upload
          ref="fileUpload"
          v-model="model.fileIds"
          drag
          :is-remove-delete-link="removeDeleteEnable"
          :selectable="selectable"
          :bind-biz-fun="bizFun"
          :is-show-batch-delete="false"
          :before-upload="uploadBeforeCheck"
          :table-selectable="tableSelectable"
        />
      </f-form-item>
      <div>fileIds: {{ model.fileIds }}</div>
      <f-form-item label="f-single-upload">
        <f-single-upload
          ref="sUpload"
          v-model="mdV"
          v-model:fileData="info"
          :before-upload="uploadByMySelf"
          :auto-upload="false"
          :is-remove-in-service="false"
          :on-change="handlerChange"
          :show-remove="false"
          :data="{
            a: 'name',
            b: [1, 2, 3, 4],
          }"
        />
      </f-form-item>
      <f-form-item label="f-signature-upload">
        <f-signature-upload
          v-model="mdV"
          v-model:fileData="info"
          :before-upload="uploadByMySelf"
          :file-type="['txt', 'md']"
          :data="{
            a: 'name',
            b: [1, 2, 3, 4],
          }"
        />
      </f-form-item>
      <!-- <f-form-item label="f-upload">
        <f-upload
          class="upload-demo"
          action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
          drag
          :limit="1"
          :httpRequest="uploadByMySelf"
          :before-upload="uploadByMySelf"
          :on-change="uploadByMySelf"
        >
        </f-upload>
      </f-form-item> -->
    </f-form>
  </div>
  <div>
    <p>mdv: {{ mdV }}</p>
    <p>info: {{ info }}</p>
  </div>
  <div>
    <f-table :data="model.fields">
      <f-table-column prop="field" label="字段" />
      <f-table-column prop="title" label="标题" />
      <f-table-column prop="value" label="值">
        <template #default="{ row }">
          <f-single-upload v-model="row.value" />
        </template>
      </f-table-column>
    </f-table>
  </div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { View } from '@dtg/frontend-plus-icons'
import {
  FAttmUpload,
  FButton,
  FDivider,
  FForm,
  FFormItem,
  FLink,
  FMessageBox,
  FSignatureUpload,
  FSingleUpload,
  FTable,
  FTableColumn,
  FUpload,
} from '@frontend-plus/components'

const fileUpload = ref()
const mdV = ref('9f5dcf4d-7259-4a03-8920-ad0151b8b3c0')
const info = reactive({})

interface Model {
  fileIds: string[]
}

const sUpload = ref()
const handlerChange = (file: any) => {}
const model = reactive({
  fileIds: [],
  fields: [
    {
      field: 'name',
      title: '姓名',
    },
    {
      field: 'age',
      title: '年龄',
    },
    {
      field: 'date',
      title: '日期',
    },
    {
      field: 'address',
      title: '地址',
    },
  ],
})

const clearFile = () => {
  model.fileIds = []
}

const addFile = () => {
  model.fileIds.push(`d0e12148-6c01-47dc-94da-3ac7aeb0${Math.random() * 10000}`)
}
// const fileIds = reactive([])
const bizFun = (res: any) => {
  console.log(res)
}
const selectable = (row: any) => {
  // console.log(row)
  return true
}

const handlePreview = (row: any) => {
  alert(JSON.stringify(row))
}

const uploadBeforeCheck = (file: File) => {
  console.log(file)
  return new Promise((resolve, reject) => {
    FMessageBox.confirm('是否上传该文件？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        console.log('确定')
        resolve(true)
      })
      .catch(() => {
        console.log('取消')
        reject(false)
      })
  })
}

const uploadByMySelf = (aa: any, bb: any, ccc: any) => {
  return new Promise((resolve, reject) => {
    resolve(true)
  })
}

const removeDeleteEnable = (row: any) => {
  // console.log('row', row)
  if (['1', '5'].includes(row.fileId)) return true
  return false
}

const tableSelectable = (row) => {
  console.log(row)
  return false
}

onMounted(() => {
  // model.fileIds.push('d0e12148-6c01-47dc-94da-3ac7aeb081a3')
})
</script>
