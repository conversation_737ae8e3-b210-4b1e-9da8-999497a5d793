<template>
  <div>
    <div>Formula</div>
    <div>{{ message }}</div>
    <div class="container">
      <pre class="code">{{ state.fomula }}</pre>
      <f-form ref="form" :column="1" :model="state">
        <f-form-item label="公式输入" prop="fomula" required>
          <f-formula-input
            ref="input"
            v-model="state.fomula"
            :row="3"
            :enabled-type-keys="state.enabledKeys"
          >
            <f-formula-extend name="资金余额" :data="state.autoSearch" />
            <f-formula-extend name="extend" :data="state.autoType" />
          </f-formula-input>
        </f-form-item>
      </f-form>
      <div>
        输入
        <div>
          符号
          <f-button @click="insertSymbol('+')">+</f-button>
          <f-button @click="insertSymbol('-')">-</f-button>
          <f-button @click="insertSymbol('>=')">>=</f-button>
          <f-button @click="backspace">{{ '<--' }}</f-button>
        </div>
        <div>
          数值
          <f-button @click="insertNumber('1')">1</f-button>
          <f-button @click="insertNumber('2')">2</f-button>
          <f-button @click="insertNumber('3')">3</f-button>
          <f-button @click="insertNumber('4')">4</f-button>
          <f-button @click="insertNumber('5')">5</f-button>
          <f-button @click="insertNumber('6')">6</f-button>
          <f-button @click="insertNumber('7')">7</f-button>
          <f-button @click="insertNumber('.')">.</f-button>
        </div>
        <div>
          函数
          <f-button @click="insertFunction('SUM')">SUM</f-button>
        </div>
        <div>
          变量
          <f-button @click="insertVarible('资金余额')">资金余额</f-button>
        </div>
        <div>
          自定义
          <f-button @click="insertCustomize()">插入自定义内容</f-button>
        </div>
        <div>
          <f-button @click="validForm">验证表单</f-button>
        </div>
      </div>
      <pre class="code">{{ stateString }}</pre>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, onMounted, reactive } from 'vue'
import {
  FButton,
  FFormulaInput,
  FFormulaExtend,
  FormulaUnit,
  FForm,
  FFormItem,
} from '@frontend-plus/components'

const message = ref('Hello, Formula!')
const input = ref()
const form = ref()

const state = reactive({
  fomula: [] as any[],
  enabledKeys: [] as string[],
  autoSearch: [] as string[],
  autoType: [] as string[],
})

onMounted(() => {
  state.fomula = [
    {
      value: 55555555555555555555.33e100,
      type: FormulaUnit.Number,
      label: '1.24',
      superType: '121314',
    },
    {
      value: 'SUM',
      type: FormulaUnit.Customize,
      label: 'SUM',
      name: 'extend',
      superType: '121314',
    },
    {
      value: '+',
      type: FormulaUnit.Operator,
      label: '+',
      superType: '121314',
    },
    {
      value: 2.65,
      type: FormulaUnit.Number,
      label: '2.65',
    },
    {
      value: 'SUM2',
      type: FormulaUnit.Customize,
      label: 'SUM2',
      name: 'extend',
    },
    {
      value: '资金余额',
      type: FormulaUnit.Variable,
      label: '资金余额',
      name: '资金余额',
      superType: '121314',
      qqqqq: '4215125',
    },
  ]

  state.enabledKeys = [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '0',
    '+',
    '-',
    '.',
    '>',
    '=',
    '!',
    '&',
    '|',
  ]
  state.autoSearch = ['资金‘', '资金余额期末数', '资金余额期初数']
  state.autoType = ['number', 'extend', 'qqqq', 'xxxx']
})

const stateString = computed(() => {
  return JSON.stringify(input?.value?.__state, null, 2)
})

const validForm = () => {
  form.value.validate((valid: any) => {
    if (valid) {
      console.log('submit!', valid)
    } else {
      console.log('error submit!', form)
      return false
    }
  })
}

const backspace = () => {
  input.value.backspace()
}

const insertSymbol = (value: string) => {
  input.value.insertValue({
    value,
    type: FormulaUnit.Operator,
  })
}

const insertFunction = (value: string) => {
  input.value.insertValue({
    value,
    type: FormulaUnit.Function,
    bracketExtend: {
      left: { a: 'b' },
      right: { c: 'd' },
    },
  })
}

const insertNumber = (value: string) => {
  if (value === '.') {
    input.value.insertValue({
      value,
      type: FormulaUnit.Symbol,
    })
    return
  }

  input.value.insertValue({
    value,
    type: FormulaUnit.Number,
  })
}

const insertVarible = (value: string) => {
  input.value.insertValue({
    value: '资金余额期末数',
    type: FormulaUnit.Variable,
    extra: {
      qq: 'qq',
      type: 'number',
      data: {
        value: 100,
      },
    },
  })
}

const insertCustomize = () => {
  input.value.insertCustomize('extend')
}
</script>

<style>
.container {
  width: 700px;
  height: 600px;
  margin: 0 auto;
  background-color: #f0f0f0;
}

.code {
  height: 300px;
  background-color: #fff;
  overflow: auto;
  background-color: #f0f0f0;
}
</style>
