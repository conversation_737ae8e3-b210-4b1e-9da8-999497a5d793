<template>
  <div class="app-wrapper">
    <div class="sidebar">
      <ul class="sidebar-links">
        <li>
          <a href="/table-transfer">表格穿梭</a>
        </li>
        <li>
          <a href="/tour">导引</a>
        </li>
        <li>
          <a href="/affix">固钉</a>
        </li>
        <li>
          <a href="/button">按钮</a>
        </li>
        <li>
          <a href="/input">输入框</a>
        </li>
        <li>
          <a href="/select">下拉框</a>
        </li>
        <li>
          <a href="/form">表单</a>
        </li>
        <li>
          <a href="/magnifier-single">单选放大镜</a>
        </li>
        <li>
          <a href="/magnifier-multi">多选放大镜</a>
        </li>
        <li>
          <a href="/amount-range">金额区间输入</a>
        </li>
        <li>
          <a href="/textarea">文本域</a>
        </li>
        <li>
          <a href="/upload">上传</a>
        </li>
        <li>
          <a href="/number">数字输入</a>
        </li>
        <li>
          <a href="/amount">金额输入</a>
        </li>
        <li>
          <a href="/amount-chinese">中文金额输入</a>
        </li>
        <li>
          <a href="/dialog">Dialog</a>
        </li>
        <li>
          <a href="/wf-history">审批记录</a>
        </li>
        <li>
          <a href="/through">流程跟踪</a>
        </li>
        <li>
          <a href="/panel">普通容器</a>
        </li>
        <li>
          <a href="/form-panel">表单容器</a>
        </li>
        <li>
          <a href="/multiple-form-panel">单表单多容器</a>
        </li>
        <li>
          <a href="/scene-status">场景状态</a>
        </li>
        <li>
          <a href="/query-scene">查询页面场景</a>
        </li>
        <li>
          <a href="/drawer-scene">抽屉场景</a>
        </li>
        <li>
          <a href="/blank-scene">简单新增场景</a>
        </li>
        <li>
          <a href="/test-multiple-form">测试-单表单多容器</a>
        </li>
        <li>
          <a href="/step-scene">步骤条新增场景</a>
        </li>
        <li>
          <a href="/across-tab-scene">横向-多页签场景</a>
        </li>
        <li>
          <a href="/vertical-tab-scene">竖向-多页签场景</a>
        </li>
        <li>
          <a href="/approval-scene">审批场景</a>
        </li>
        <li>
          <a href="/submit-state">提交组件</a>
        </li>
        <li>
          <a href="/scene-view">查看组件</a>
        </li>
        <li>
          <a href="/scene-breadcrumb">面包屑</a>
        </li>
        <li>
          <a href="/tree">树形结构</a>
        </li>
        <li>
          <a href="/query-table-card">审批列表</a>
        </li>
        <li>
          <a href="/query-table">普通表格</a>
        </li>
        <li>
          <a href="/import-scene">导入</a>
        </li>
        <li>
          <a href="/tip">提示</a>
        </li>
        <li><a href="/tree-tab-scene">tab页签tree</a></li>
        <li><a href="/print">打印</a></li>
        <li>
          <a href="/import-sunder-scene">分步骤导入</a>
          <a href="/calendar">日历</a>
        </li>
        <li>
          <a href="/drawer-scene-table">抽屉表格场景</a>
        </li>
        <li>
          <a href="/drawer-grid">抽屉+QueryGrid</a>
        </li>
        <li><a href="/tree-form-panel">tree-form-panel</a></li>
        <li><a href="/block-drag">左右拖拽</a></li>
        <li>
          <a href="/tree-form-panel">树形表单</a>
        </li>
        <li>
          <a href="/tree-table">树形表格</a>
        </li>
        <li>
          <a href="/text-icon">文字图标</a>
        </li>
        <li>
          <a href="/table-edit">可编辑表格</a>
        </li>
        <li>
          <a href="/date">日历</a>
        </li>
        <li>
          <a href="/compare-table">对比表格</a>
        </li>
        <li>
          <a href="/tree-transfer">树形穿梭框</a>
        </li>
        <li>
          <a href="/import-table">导入表格</a>
        </li>
        <li>
          <a href="/process-tracking">流程跟踪</a>
        </li>
        <li>
          <a href="/formula">公式-formula</a>
        </li>
        <li>
          <a href="/org">组织架构穿梭框-org</a>
        </li>
        <li>
          <a href="/tree-select">TreeSelect</a>
        </li>
        <li>
          <a href="/sumtable">汇总表格</a>
        </li>
        <li>
          <a href="/query-table-edit">可过滤的可编辑表格</a>
        </li>
        <li>
          <a href="/drag-transfer">可拖拽穿梭框</a>
        </li>
        <li>
          <a href="/tab">页签</a>
        </li>
      </ul>
    </div>
    <div class="page" style="height: 100%">
      <div class="content" style="height: 100%; position: relative">
        <f-config-provider
          size="small"
          show-attm-preview
          file-search-url="{file}/api/v1/file"
          :preview-url-getter="previewUrlGetter"
          :editing-show-del="true"
          :print-method="printMethod"
          :approval-his-filed="[
            'recordId',
            'systemCode',
            'transType',
            'agencyId',
            'currencyId',
          ]"
        >
          <content style="height: 100%" />
        </f-config-provider>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import { FConfigProvider } from '@frontend-plus/components'
const name = location.pathname.replace(/^\//, '') || 'affix'
const content = defineAsyncComponent(() => import(`./src/${name}.vue`))

const previewUrlGetter = (fileId: string, fileName: string) => {
  return `http://*************:9858/iss-file-view/onlinePreview?url=***************************************************************************************************************************************************************************************************************************************************************************************************%3D`
}

const printMethod = (dom: HTMLElement) => {
  console.log(dom)
}
</script>

<style lang="scss">
body {
  margin: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.app-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  .sidebar {
    font-size: 16px;
    width: 20rem;
    position: fixed;
    z-index: 10;
    top: 0;
    left: 0;
    bottom: 0;
    overflow-y: auto;
    box-sizing: border-box;
    border-right: 2px solid #eaecef;

    .sidebar-links {
      padding: 1.5rem 0;
      display: block;
      margin: 0;
      list-style-type: none;
      line-height: 1.7;

      li {
        padding-left: 1rem;
      }
    }
  }

  .page {
    padding-left: 20rem;

    .content {
      // max-width: 750px;
      margin: 0 auto;
      // padding: 2rem 2.5rem;
      height: 100%;
      background-color: #eef5fb;
    }
  }
}

#layout {
  width: 700px;
  margin: 0 auto;
}

.demo-layout-grid-content {
  border-radius: 4px;
  min-height: 36px;
  background: #99a9bf;
}
</style>
