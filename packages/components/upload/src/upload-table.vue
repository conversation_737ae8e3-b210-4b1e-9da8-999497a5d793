<template>
  <div
    :class="[
      nsUploadTable.b(),
      nsUploadTable.is(
        'no-allow-operate',
        !isShowBatchDownload && !isShowBatchDelete && !showUpload
      ),
    ]"
  >
    <div
      v-if="isShowBatchDownload || isShowBatchDelete"
      :class="nsUploadTable.b('button-group')"
    >
      <f-button
        v-if="isShowBatchDownload"
        type="primary"
        :disabled="checkList.length === 0"
        @click="handleTableBatchDownload"
      >
        {{ t('el.upload.table.batchDownload') }}
      </f-button>
      <f-button
        v-if="isShowBatchDelete"
        type="danger"
        plain
        :disabled="checkList.length === 0"
        @click="handleTableBatchDelete"
      >
        {{ t('el.upload.table.batchDelete') }}
      </f-button>
    </div>
    <f-table
      ref="tableRef"
      v-bind="$attrs"
      border
      :data="fileData"
      :header-row-class-name="() => nsUploadTable.e('table-header')"
      :selectable-all="selectableAll"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <f-table-column
        v-if="isShowCheckbox"
        type="selection"
        :selectable="props.selectable"
        align="center"
      />
      <f-table-column
        type="index"
        :label="t('el.upload.table.seq')"
        align="center"
        width="60px"
      />
      <f-table-column
        prop="name"
        :label="t('el.upload.table.fileName')"
        min-width="320px"
        sortable
      >
        <template #default="{ row }">
          <div :class="nsUploadTable.e('name')">
            <span
              :class="getPercentageClass(row)"
              :style="getPercentageWidth(row)"
            />
            <f-icon :size="20">
              <component :is="generalFileIcon(row)" />
            </f-icon>
            <span
              :class="[
                nsUploadTable.em('name', 'file-name'),
                nsUploadTable.is('fail', row.status === 'fail'),
              ]"
            >
              {{ row.name }}
            </span>
            <span :class="nsUploadTable.em('name', 'status')">
              <f-icon
                :size="14"
                :class="nsUploadTable.is('fail', row.status === 'fail')"
              >
                <component :is="generalStatusIcon(row)" />
              </f-icon>
              <span :class="nsUploadTable.em('name', `status-${row.status}`)">
                {{ generalStatusName(row) }}
              </span>
            </span>
          </div>
        </template>
      </f-table-column>
      <f-table-column
        prop="size"
        :label="t('el.upload.table.size')"
        width="100px"
        sortable
        align="center"
      >
        <template #default="{ row }">
          <span
            v-if="!['fail', 'canceled'].includes(row.status)"
            :class="nsUploadTable.b('uploading-size')"
          >
            {{ calPercentageTiSingleFile(row) }}
          </span>
          <span :class="nsUploadTable.is(row.status)">
            {{ getSize(row) }}
          </span>
        </template>
      </f-table-column>
      <f-table-column
        prop="date"
        :label="t('el.upload.uploadDate')"
        sortable
        width="130px"
        align="center"
      >
        <template #default="{ row }">
          {{ formatUploadDate(row.date, 'YYYY/MM/DD') }}
        </template>
      </f-table-column>
      <f-table-column
        prop="person"
        :label="t('el.upload.table.person')"
        align="center"
      />
      <slot name="column" />
      <f-table-column
        prop="operate"
        :label="t('el.upload.table.operate')"
        width="200px"
        fixed="right"
        :class-name="nsUploadTable.b('table-operate')"
      >
        <template #default="{ row }">
          <!-- 查看页面不显示 -->
          <f-link
            v-if="
              !row.externalFile &&
              (typeof isRemoveDeleteLink === 'function'
                ? !isRemoveDeleteLink(row)
                : !isRemoveDeleteLink)
            "
            type="danger"
            :icon="Delete"
            :underline="false"
            @click="handleRawDelete(row)"
          >
            {{ t('el.upload.delete') }}
          </f-link>
          <f-divider
            v-if="
              typeof isRemoveDeleteLink === 'function'
                ? !isRemoveDeleteLink(row)
                : !isRemoveDeleteLink
            "
            direction="vertical"
          />
          <f-link
            v-if="row.status === 'uploading'"
            type="primary"
            :icon="DtgIneffective"
            :underline="false"
            @click="handleRawCancel(row)"
          >
            {{ t('el.upload.table.cancelUpload') }}
          </f-link>
          <f-divider v-if="row.status === 'uploading'" direction="vertical" />
          <f-link
            v-if="!row?.status || row.status === 'success'"
            type="primary"
            :icon="Download"
            :underline="false"
            @click="handleRawDownload(row)"
          >
            {{ t('el.upload.download') }}
          </f-link>
          <f-link
            v-if="row?.status && row.status === 'fail'"
            type="primary"
            :icon="RefreshLeft"
            :underline="false"
            @click="handleReUpload(row)"
          >
            {{ t('el.upload.reUpload') }}
          </f-link>
          <template
            v-if="(!row?.status || row.status === 'success') && showPreview"
          >
            <f-divider direction="vertical" />
            <f-file-preview-button
              tag="link"
              type="primary"
              :file-id="row.fileId"
            />
          </template>
          <slot name="link" :row="row" />
        </template>
      </f-table-column>
    </f-table>
  </div>
</template>
<script setup lang="ts">
import { ref, shallowRef } from 'vue'
import dayjs from 'dayjs'
import {
  CircleClose,
  Delete,
  Download,
  DtgDoc,
  DtgIneffective,
  DtgOtherType,
  DtgPdf,
  DtgPpt,
  DtgTxt,
  DtgWarning,
  DtgXls,
  RefreshLeft,
} from '@dtg/frontend-plus-icons'
import { FTable, FTableColumn } from '@frontend-plus/components/table'
import { FButton, FDivider, FIcon, FLink } from '@frontend-plus/components'
import { useLocale, useNamespace } from '@frontend-plus/hooks'
import { FFilePreviewButton } from '@frontend-plus/scene/file-preview-button'
import { uploadTableProps } from './upload-table'
import { useFileVaildate } from './use-file-vaildate'
import type { TableColumnType } from './upload-table'
import type { Component } from 'vue'

type IconMapperType = {
  xls: Component
  xlsx: Component
  pdf: Component
  doc: Component
  default: Component
  txt: Component
  ppt: Component
}

type HandlerFileType = (row: TableColumnType) => void

const props = defineProps(uploadTableProps)

const emits = defineEmits([
  'delete',
  'canceld',
  'download',
  're-upload',
  'batch-download',
  'batch-delete',
])

const nsUploadTable = useNamespace('upload-table')
const { calFileSize } = useFileVaildate()

const { t } = useLocale()

const tableRef = shallowRef() // 表格模板
const checkList = ref<any[]>([]) // 存储已选

const iconMapper: IconMapperType = {
  xls: DtgXls,
  xlsx: DtgXls,
  pdf: DtgPdf,
  doc: DtgDoc,
  default: DtgOtherType,
  txt: DtgTxt,
  ppt: DtgPpt,
}

const selectableAll = (rows: TableColumnType[]) => {
  const selectable = !rows.some((row) => {
    return !row?.status || row.status === 'success'
  })
  if (props.tableSelectableAll && !selectable) {
    return !props.tableSelectableAll(rows)
  }
  return selectable
}

const calPercentageTiSingleFile = (row: TableColumnType) => {
  if (row.percentage) {
    if (row.percentage < 100) {
      const _percentage = Math.floor((row.percentage / 100) * Number(row.size))
      return `${calFileSize(_percentage)}/`
    } else if (row.percentage === 100) {
      return ''
    }
  }
  return ''
}

const getPercentageClass = (row: TableColumnType) => {
  if (row.percentage) {
    if (row.percentage < 100 && row.status === 'uploading') {
      return nsUploadTable.em('name', 'percent')
    } else if (row.status === 'fail') {
      return nsUploadTable.em('name', 'error')
    } else if (row.status === 'canceled') {
      return nsUploadTable.em('name', 'canceled')
    }
  }
}

const getPercentageWidth = (row: TableColumnType) => {
  if (row.percentage) {
    if (row.percentage < 100 && row.status === 'uploading') {
      return {
        width: `${row.percentage}%`,
      }
    } else if (row.status === 'fail') {
      return {
        width: '100%',
      }
    } else if (row.status === 'canceled') {
      return {
        width: '0',
      }
    }
  }
}

const generalStatusIcon = (row: TableColumnType) => {
  if (row.status === 'canceled') {
    return DtgWarning
  } else if (row.status === 'fail') {
    return CircleClose
  }
}

const generalStatusName = (row: TableColumnType) => {
  if (row.status === 'canceled') {
    return t('el.upload.table.canceledStatus')
  } else if (row.status === 'fail') {
    return t('el.upload.table.failStatus')
  }
}

const generalFileIcon = (row: TableColumnType) => {
  if (row.name) {
    const fileType: keyof IconMapperType = row.name
      .slice(Math.max(0, row.name.lastIndexOf('.') + 1))
      .toLowerCase() as keyof IconMapperType
    if (iconMapper[fileType]) {
      return iconMapper[fileType]
    } else {
      return iconMapper['default']
    }
  }
}

const getSize = (row: TableColumnType) => {
  if (!row.status || !['fail', 'canceled'].includes(row.status)) {
    return `${calFileSize(Number(row.size))}`
  }
  return '0B'
}

// 点击单个checkbox
const handleSelect = (selection: TableColumnType[]) => {
  checkList.value = selection
}

// 点击全选checkbox
const handleSelectAll = (selection: TableColumnType[]) => {
  checkList.value = selection
}

// 操作列删除
const handleRawDelete: HandlerFileType = (row) => {
  emits('delete', row)
}

// 操作列取消上传
const handleRawCancel: HandlerFileType = (row) => {
  emits('canceld', row)
}

// 操作列下载
const handleRawDownload: HandlerFileType = (row) => {
  emits('download', row)
}

// 操作列重新上传
const handleReUpload: HandlerFileType = (row) => {
  row.percentage = 0
  emits('re-upload', row)
}

// 处理批量下载
const handleTableBatchDownload = () => {
  emits('batch-download', checkList.value)
}

// 处理批量删除
const handleTableBatchDelete = () => {
  emits('batch-delete', checkList.value)
}

// 处理日期格式化
const formatUploadDate = (date: Date | string, format: string) => {
  return dayjs(date).format(format)
}

// 批量操作后清空已选
const clearSlected = () => {
  checkList.value.splice(0)
  tableRef.value.clearSelection()
}

const removeRow = (row: TableColumnType) => {
  const index = checkList.value.indexOf(row)

  if (index > -1) {
    checkList.value.splice(index, 1)
  }
}

defineExpose({
  // checked list
  checkList,
  // table ref
  tableRef,
  clearSlected,
  removeRow,
})
</script>
