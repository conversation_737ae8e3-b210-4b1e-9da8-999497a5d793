import { buildProps, definePropType } from '@frontend-plus/utils'
import type { ExtractPropTypes, PropType } from 'vue'
import type UploadTable from './upload-table.vue'
import type { UploadFile, UploadRawFile } from './upload'

export interface TableColumnType {
  name?: string
  size: number
  date?: string | Date
  person?: string
  operate?: string
  uid?: number
  percentage?: number
  status?: string
  file?: UploadFile | UploadRawFile
  fileId: string
  response?: Record<string, any>
  newUpload?: boolean
  externalFile?: boolean
  filePath?: string
  controller?: any
}

export const uploadTableProps = buildProps({
  /**
   * 是否显示操作列
   */
  isShowOperate: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示批量删除
   */
  isShowBatchDelete: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示批量下载
   */
  isShowBatchDownload: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示批量下载
   */
  isShowDownload: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示预览
   */
  isShowPreview: {
    type: Boolean,
    default: false,
  },
  /**
   * 表格数据
   */
  fileData: {
    type: definePropType<TableColumnType[]>(Array),
    default: () => [],
    required: false,
  },
  /**
   * 是否显示checkbox列
   */
  isShowCheckbox: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示批量删除
   */
  isShowDelete: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否隐藏操作列删除
   */
  isRemoveDeleteLink: {
    type: [Boolean, Function] as PropType<
      boolean | ((row: TableColumnType) => boolean)
    >,
    default: false,
  },
  /**
   * checkbox禁用钩子
   */
  selectable: {
    type: Function,
    default: () => true,
    required: false,
  },
  /**
   * 外层是否显示上传框
   */
  showUpload: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否展示预览按钮
   */
  showPreview: Boolean,
  /**
   * 表格上面的全选框
   */
  tableSelectableAll: {
    type: Function,
    required: false,
  },
})

export type UploadTableProps = ExtractPropTypes<typeof uploadTableProps>
export type UploadTableInstance = InstanceType<typeof UploadTable>
