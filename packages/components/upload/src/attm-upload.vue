<template>
  <div :class="nsUpload.b()">
    <f-upload
      v-if="showUpload"
      ref="uploadRef"
      v-bind="$attrs"
      :multiple="multiple"
      :auto-upload="autoUpload"
      :http-request="httpRequest"
      :before-upload="handleBeforeUpload"
      :on-progress="handleProgress"
      :on-change="handleChange"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :limit="limit"
      :data="extraData"
      :on-error="handleError"
      :on-remove="handleRemove"
      :show-file-list="false"
      :action="actionUrl"
      :on-size-exceed="onSizeExceed"
      :name="name"
    >
      <template #default>
        <slot name="default" />
      </template>
      <template #trigger>
        <slot name="trigger">
          <div v-show="!isStarting || showSingleInfo">
            <DtgUpload :class="nsUpload.m('icon')" />
            <div v-if="!isdrag" :class="nsUpload.e('tips')">
              <span v-if="!slots.undragTips">{{ t('el.upload.dragTip') }}</span>
              <span v-if="!slots.undragTips">
                {{ t('el.upload.dragTips') }}
              </span>
              <slot name="undragTips" />
            </div>
            <div v-else>
              <span v-if="!slots.ondragTips">
                {{ t('el.upload.realease') }}
              </span>
              <slot name="ondragTips" />
            </div>
          </div>
          <!-- 单个文件上传 -->
          <div v-show="!multiple && isStarting && !showSingleInfo">
            <div :class="nsUpload.b('info')">
              <div :class="nsUpload.b('status')">
                <span>{{ fileName }}</span>
                <span
                  v-if="!isFail && percentage == 100"
                  :class="nsUpload.bm('status', 'success')"
                >
                  {{ t('el.upload.success') }}
                </span>
                <span
                  v-if="isFail && percentage == 100"
                  :class="nsUpload.bm('status', 'error')"
                >
                  {{ t('el.upload.fail') }}
                </span>
                <f-progress
                  v-if="percentage !== 100"
                  :percentage="percentage"
                  :color="progressColor"
                />
              </div>
              <div :class="nsUpload.b('detail')">
                <div :class="nsUpload.bm('detail', 'size')">
                  {{ t('el.upload.fileSize') }}：{{ singleFileSize }}
                </div>
                <div :class="nsUpload.bm('detail', 'date')">
                  {{ t('el.upload.uploadDate') }}：{{ uploadDate }}
                </div>
              </div>
              <div :class="nsUpload.b('tools')">
                <f-link
                  v-show="percentage === 100 && !isFail"
                  :underline="false"
                  type="primary"
                >
                  {{ t('el.upload.continue') }}
                </f-link>
                <f-link
                  v-show="isFail"
                  :underline="false"
                  type="primary"
                  @click.stop="handleReUpload"
                >
                  {{ t('el.upload.reUpload') }}
                </f-link>
                <f-link
                  :underline="false"
                  type="danger"
                  @click.stop="handleSingleModeDel"
                >
                  {{ t('el.upload.delete') }}
                </f-link>
              </div>
            </div>
          </div>
        </slot>
      </template>
      <template #tip>
        <slot name="tip" />
      </template>
      <template #file>
        <slot name="file" />
      </template>
    </f-upload>
    <!-- 上传文件历史 -->
    <f-upload-table
      v-if="multiple && showFileTable"
      ref="uploadTableRef"
      :file-data="fileTableData"
      :is-show-batch-delete="isShowBatchDelete"
      :is-show-batch-download="isShowBatchDownload"
      :is-remove-delete-link="isRemoveDeleteLink"
      :show-upload="showUpload"
      :show-preview="previewEnable"
      :selectable="tableSelectable"
      :selectable-all="tableSelecttableAll"
      :table-selectable-all="tableSelectableAll"
      @canceld="handleTableCancel"
      @delete="handleTableDelete"
      @download="handleTableDownload"
      @re-upload="handleTableReUpload"
      @batch-delete="handleTableBatchDelete"
      @batch-download="handleTableBatchDownload"
    >
      <template #link="{ row }">
        <slot name="link" :row="row" />
      </template>
    </f-upload-table>
  </div>
</template>
<script lang="ts" setup>
import {
  type Slots,
  computed,
  onUnmounted,
  provide,
  ref,
  shallowRef,
  useSlots,
  watchEffect,
} from 'vue'
import { DtgUpload } from '@dtg/frontend-plus-icons'
import dayjs from 'dayjs'
import { changedragKey } from '@frontend-plus/tokens'
import { processBeforeTrigger, useFtcRes } from '@frontend-plus/utils'
import { FUpload, FUploadTable } from '@frontend-plus/components/upload'
import FProgress from '@frontend-plus/components/progress'
import { FLink } from '@frontend-plus/components/link'
import { FMessage } from '@frontend-plus/components/message'
import { useGlobalConfig, useLocale, useNamespace } from '@frontend-plus/hooks'
import { useUploadApi } from './useUploadApi'
import { FileStatus, attmUploadProps, attmUploadgEmits } from './attm-upload'
import { useFileVaildate } from './use-file-vaildate'
import type { TableColumnType } from './upload-table'
import type {
  UploadFile,
  UploadFiles,
  UploadHooks,
  UploadProgressEvent,
  UploadRawFile,
  UploadUserFile,
} from './upload'

defineOptions({
  name: 'FAttmUpload',
})

const props = defineProps(attmUploadProps)
const emits = defineEmits(attmUploadgEmits)

const { t } = useLocale()
const slots: Slots = useSlots()

const nsUpload = useNamespace('attm-upload')

const {
  calFileSize,
  vaildFileType,
  vaildMultipleSize,
  vaildSingeSize,
  vaildMultipleTotalFileSize,
} = useFileVaildate(props)

const {
  fileTableData,
  getFile,
  initFileTable,
  handleRemoveToBack,
  handleDownload,
  handleDownloadBatch,
  handleExternalDownload,
  handleDeleteBatch,
  httpRequest,
} = useUploadApi(props, emits)

const uploadUrl = useGlobalConfig('uploadUrl')
const previewEnable = computed(() => {
  return props.showPreview || useGlobalConfig('showAttmPreview').value
})

const uploadRef = shallowRef()
const uploadTableRef = shallowRef()
const percentage = ref<number>(0) // 进度条
const fileName = ref<string>('') // 单个上传时显示的文件名称
const isStarting = ref<boolean>(false) // 单个上传时的状态
const uploadDate = ref<string>() // 单个上传的时间
const progressColor = ref<string>('#48C79C') // 单个上传的进度条颜色
const isFail = ref<boolean>(false) // 单个上传时文件是否上传失败
const fileList = ref<any[]>([]) // change事件保存的文件列表（对于选多个的时候）
const singleFileSize = ref<string>() // 单个文件的文件大小信息
const singleFileInstance = ref<UploadFile>() // 单个文件上传时存储的文件实例，用于中断上传与失败时重新上传
const isdrag = ref(false)

const actionUrl = computed(() => {
  return props.serviceUrl || uploadUrl.value
})

const fileData = computed(() => {
  const _fileData = fileTableData.value.filter((file) => {
    return file.status === FileStatus.Success
  })
  return _fileData.map((file) => {
    return file.response
  })
})

// 单个文件点击右下角删除
const handleSingleModeDel = (): void => {
  const { abort } = uploadRef.value
  if (percentage.value < 100) {
    // 上传过程中,中止上传
    abort(singleFileInstance.value)
  }
  isStarting.value = false
  percentage.value = 0
  singleFileInstance.value = undefined
  if (props.singleDelete) {
    props.singleDelete()
  }
}

const handleExceed = () => {
  FMessage({
    showClose: true,
    message: `当前上传文件数量不得超出${props.limit}个`,
    type: 'warning',
  })
}

const onSizeExceed = (files: any) => {
  return vaildMultipleTotalFileSize(props, t, fileTableData.value, files)
}

// 重新上传
const handleReUpload = (): void => {
  const { handleStart, submit, abort } = uploadRef.value
  // 上传失败
  if (isFail.value) {
    handleStart(singleFileInstance.value?.raw)
    submit(singleFileInstance.value)
  } else if (percentage.value < 100) {
    // 上传过程中点击重新上传
    abort(singleFileInstance.value)
    handleStart(singleFileInstance.value?.raw)
    submit()
  }
}
// 上传之前
const handleBeforeUpload = async (file: UploadRawFile) => {
  percentage.value = 0

  const addTableRow = () => {
    if (!props.multiple) {
      isStarting.value = true
      fileName.value = file.name
      uploadDate.value = dayjs(new Date()).format('YYYY-MM-DD')
      singleFileSize.value = calFileSize(Number(file.size))
    } else {
      const raw: TableColumnType = {
        name: file.name,
        uid: file.uid,
        size: file.size,
        date: new Date(),
        person: '',
        percentage: 0,
        status: FileStatus.BeforeUpload,
        file,
        fileId: '',
      }

      if (getFile(raw) === -1) {
        fileTableData.value.push(raw)
      }

      emits('update:fileData', fileTableData.value)
    }
  }

  if (!vaildFileType(file)) {
    return false
  }
  if (!vaildSingeSize(file, fileList.value)) {
    return false
  }
  if (!vaildMultipleSize(file, fileList.value)) {
    return false
  }

  if (!props.beforeUpload) {
    addTableRow()
    return true
  }

  let result = false
  try {
    result = await props.beforeUpload(file)
    if (result) {
      addTableRow()
    }
  } catch {
    result = false
  }
  return result
}
// Change
const handleChange = (file: UploadFile, files: UploadUserFile[]) => {
  singleFileInstance.value = file
  fileList.value = files // 存储选择多个文件时的列表
}
// 上传成功
const handleSuccess = (
  response: any,
  uploadFile: UploadFile,
  uploadFiles: UploadFiles
) => {
  if (response?.data && response.data.error) {
    // 将界面上显示的已经上传成功的文件删除掉
    uploadFiles.splice(uploadFiles.indexOf(uploadFile), 1)
    if (props.multiple && props.showFileTable) {
      const index = getFile(uploadFile)
      fileTableData.value[index].status = FileStatus.Fail
    }
    props.onError(response, uploadFile, uploadFiles)
  } else if (response?.data && response.data.success) {
    if (props.multiple) {
      const ftc = useFtcRes(response.data)
      if (ftc && ftc.success) {
        const { fileName, id, inputTime, dataSize, person } = ftc.data
        isFail.value = false
        const index = getFile(uploadFile)
        fileTableData.value[index].status = FileStatus.Success
        fileTableData.value[index].name = fileName
        fileTableData.value[index].fileId = id
        fileTableData.value[index].date = inputTime
        fileTableData.value[index].size = dataSize
        fileTableData.value[index].person = person
        fileTableData.value[index].response = ftc.data
        fileTableData.value[index].newUpload = true

        if (Array.isArray(props.modelValue) && !props.modelValue.includes(id)) {
          emits('update:modelValue', [...props.modelValue, id])
        }
        // 手动绑定业务
        if (props.bindBizFun) {
          props.bindBizFun(response.data.data, uploadFile, uploadFiles)
          props.onSuccess(response, uploadFile, uploadFiles)
        }
      }
    } else {
      props.onSuccess(response, uploadFile, uploadFiles)
    }
  }
}

// 上传失败
const handleError: UploadHooks['onError'] = (err, file) => {
  if (!props.multiple) {
    isFail.value = true
  } else {
    const index = getFile(file)
    fileTableData.value[index].status = FileStatus.Fail
  }
  props.onError(err, file)
}
// 移除时
const handleRemove = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (uploadFile.status === FileStatus.Success) {
    handleRemoveToBack(uploadFile, false)
  }
  props.onRemove(uploadFile, uploadFiles)
}
// 文件上传时
const handleProgress = (evt: UploadProgressEvent, uploadFile: UploadFile) => {
  const { percent, controller } = evt

  const _percent = Math.floor(Number(percent))
  if (props.multiple) {
    const index = getFile(uploadFile)
    fileTableData.value[index].percentage = _percent
    fileTableData.value[index].controller = controller

    if ((fileTableData.value[index].percentage as number) < 100) {
      fileTableData.value[index].status = FileStatus.Uploading
      emits('update:fileData', fileTableData.value)
    }
  } else {
    percentage.value = _percent
  }
}
// 处理表格操作列删除
const handleTableDelete = async (row: TableColumnType) => {
  if (props.beforeDelete) {
    const _result = await processBeforeTrigger(() => {
      return props.beforeDelete!(row) as any
    })
    if (!_result) {
      return
    }
  }
  // 对于不是上传中的文件 直接进行删除
  const fileIndex = getFile(row)
  if (!row?.status || row.status !== FileStatus.Uploading) {
    if (row.fileId) {
      handleRemoveToBack(row.fileId, row.newUpload).then(() => {
        emits(
          'update:modelValue',
          Array.isArray(props.modelValue)
            ? props.modelValue.filter((x) => x !== row.fileId)
            : []
        )

        fileTableData.value.splice(fileIndex, 1)
        emits('update:fileData', fileTableData.value)
        props.onRemove([row.fileId], row)
        uploadTableRef.value.removeRow(row)
      })
    } else {
      // 对于失败状态的删除
      if (row.status === FileStatus.Fail) {
        fileTableData.value.splice(fileIndex, 1)
        emits('update:fileData', fileTableData.value)
        props.onRemove([], row)
      }
    }
  } else if (row.status === FileStatus.Uploading) {
    if (row.fileId) {
      // 对于上传中的文件 需要中止上传
      const { handleRemove } = uploadRef.value
      handleRemove(row.file)
      handleRemoveToBack(row.fileId, row.newUpload).then(() => {
        const index = getFile(row)
        fileTableData.value.splice(index, 1)
        emits('update:fileData', fileTableData.value)
        props.onRemove([row.fileId], row)
      })
    }
  }
}
// 处理表格中处于上传中的文件的取消上传操作
const handleTableCancel = (row: TableColumnType): void => {
  const { abort } = uploadRef.value
  const index = getFile(row)

  abort(row.file)

  fileTableData.value[index]?.controller?.abort()
  fileTableData.value[index].status = FileStatus.Canceled

  emits('update:fileData', fileTableData.value)
}

// 处理表格中上传成功的文件的下载操作
const handleTableDownload = (row: TableColumnType): void => {
  if (row.status === FileStatus.Success && row.fileId) {
    if (row.externalFile) {
      handleExternalDownload(row)
      return
    }
    handleDownload(row.fileId)
  }
}

// 处理表格中上传失败的文件的重新上传操作
const handleTableReUpload = (row: TableColumnType): void => {
  const { handleStart, submit } = uploadRef.value
  handleStart(row.file)
  submit()
}

// 处理表格批量删除
const handleTableBatchDelete = async (fileChecked: TableColumnType[]) => {
  if (props.beforeBatchDelete) {
    const _result = await processBeforeTrigger(() => {
      return props.beforeBatchDelete!(fileChecked) as any
    })
    if (!_result) {
      return
    }
  }
  handleDeleteBatch(fileChecked)
  const _fileIds = fileChecked.map((file) => {
    return file.fileId
  })
  emits(
    'update:modelValue',
    Array.isArray(props.modelValue)
      ? props.modelValue.filter((x) => !_fileIds.includes(x))
      : []
  )

  props.onRemove(_fileIds, fileChecked)
  uploadTableRef.value.clearSlected()
}
// 处理批量下载
const handleTableBatchDownload = (fileChecked: TableColumnType[]) => {
  if (fileChecked.length > 0) {
    const params: any[] = fileChecked
      .filter((_file) => {
        if (_file.fileId) {
          return true
        }
        return false
      })
      .map((file) => {
        return file.fileId
      })
    handleDownloadBatch(params)
    uploadTableRef.value.clearSlected()
  }
}
// 处理单行checkbox是否禁用
const tableSelectable = (row: TableColumnType) => {
  if (props.tableSelectable) {
    return row.status === FileStatus.Success && props.tableSelectable(row)
  }
  return row.status === FileStatus.Success
}
// 处理表格所有checkbox的禁用状态
const tableSelecttableAll = (rows: any): boolean => {
  return rows.some((row: any) => {
    return row.status !== FileStatus.Success
  })
}

const changeDrag = (val: boolean) => {
  isdrag.value = val
}

const clear = () => {
  fileTableData.value.splice(0)
  emits('update:modelValue', [])
  emits('update:fileData', [])
}

const init = (externalfileIds: string[], isclear = true) => {
  if (isclear) {
    clear()
  }
  initFileTable(externalfileIds)
  emits(
    'update:modelValue',
    fileTableData.value.map((item) => {
      return item.fileId ?? item.uid
    })
  )
}

onUnmounted(() => {
  clear()
})

watchEffect(() => {
  if (Array.isArray(props.modelValue)) {
    if (props.modelValue.length > 0) {
      const fileTableIds = fileTableData.value.map((x) => x.fileId)
      if (props.modelValue.some((x) => !fileTableIds.includes(x))) {
        initFileTable(props.modelValue)
      }
    } else {
      fileTableData.value.splice(0)
      emits('update:fileData', [])
    }
  } else {
    clear()
  }
})

provide(changedragKey, changeDrag)

defineExpose({
  uploadTable: uploadTableRef,
  fileData,
  init,
})
</script>
