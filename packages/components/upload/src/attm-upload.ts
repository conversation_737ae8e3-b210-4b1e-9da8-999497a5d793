import { NOOP } from '@vue/shared'
import { buildProps, mutable } from '@frontend-plus/utils'
import { uploadTableProps } from './upload-table'
import type AttmUpload from './attm-upload.vue'
import type { ExtractPropTypes, PropType } from 'vue'

type StorageUnit = 'KB' | 'MB' | 'GB'

export enum FileStatus {
  Success = 'success',
  Fail = 'fail',
  Uploading = 'uploading',
  Ready = 'ready',
  BeforeUpload = 'beforeUpload',
  Canceled = 'canceled',
}

export const attmUploadProps = buildProps({
  ...uploadTableProps,
  /**
   * 单个文件大小，配合单位unit使用
   */
  singleSize: {
    type: Number,
    default: 20,
  },
  /**
   * 所有文件大小，配合单位unit使用
   */
  totalSize: {
    type: Number,
    default: 60,
  },
  /**
   * 单位
   */
  unit: {
    type: String as PropType<StorageUnit>,
    default: 'MB',
  },
  /**
   * 文件类型
   */
  fileType: {
    type: Array,
    default: () => {
      return [
        'xls',
        'xlsx',
        'docx',
        'png',
        'jpg',
        'jpeg',
        'gif',
        'bmp',
        'pdf',
        'csv',
        'txt',
        'doc',
        'zip',
        'rar',
        '7z',
        'md',
      ]
    },
  },
  /**
   * 超时时间
   */
  timeout: Number,
  /**
   * 上传前的钩子
   */
  beforeUpload: Function,
  /**
   * 是否显示文件列表
   */
  showFileList: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示上传历史表格
   */
  showFileTable: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示左侧上传框
   */
  showUpload: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否自动上传
   */
  autoUpload: {
    type: Boolean,
    default: true,
  },
  /**
   * 文件上传成功的钩子函数
   */
  onSuccess: {
    type: Function,
    default: NOOP,
  },
  /**
   * 文件上传失败的钩子函数
   */
  onError: {
    type: Function,
    default: NOOP,
  },
  /**
   * 是否自动与业务绑定
   */
  isAutoBind: {
    type: Boolean,
    default: false,
  },
  /**
   * 上传时附带的额外参数
   */
  extraData: {
    type: Object,
    default: () => mutable({} as const),
  },
  /**
   * 是否可以多选
   */
  multiple: {
    type: Boolean,
    default: true,
  },
  /**
   * 上传至文件服务之后的钩子,用于绑定业务
   */
  bindBizFun: {
    type: Function,
    default: NOOP,
  },
  /**
   * 点击文件列表中已上传文件的钩子函数
   */
  onPreview: {
    type: Function,
    default: NOOP,
  },
  /**
   * 移除文件的钩子函数
   */
  onRemove: {
    type: Function,
    default: NOOP,
  },
  /**
   * 与业务绑定的URL
   */
  bindUrl: {
    type: String,
    default: '',
  },
  /**
   * fileIds
   */
  modelValue: {
    type: Array as PropType<string[]>,
    required: false,
    default: () => [],
  },
  /**
   * 上传地址
   */
  serviceUrl: {
    type: String,
    default: '',
  },
  /**
   * 上传文件的字段名
   */
  name: {
    type: String,
    default: 'file',
  },
  /**
   * 单个文件上传时是否显示信息
   */
  showSingleInfo: {
    type: Boolean,
    default: true,
  },
  /**
   * 单个文件上传信息框中删除的回调
   */
  singleDelete: {
    type: Function,
    default: NOOP,
  },
  limit: Number,
  /**
   * 单笔删除之前的钩子
   */
  beforeDelete: {
    type: Function,
    required: false,
  },
  /**
   * 所有文件大小，配合单位unit使用
   */
  allTotalSize: {
    type: Number,
    default: 200,
  },
  /**
   * 批量删除前的钩子
   */
  beforeBatchDelete: {
    type: Function,
    required: false,
  },
  /**
   * 是否展示预览按钮
   */
  showPreview: Boolean,
  /**
   * 是否忽略全局错误提示
   */
  ignoreGlobalErrorTip: {
    type: Boolean,
    default: false,
  },
  /**
   * 表格前面的选择框
   */
  tableSelectable: {
    type: Function,
    required: false,
  },
  /**
   * 表格上面的全选框
   */
  tableSelectableAll: {
    type: Function,
    required: false,
  },
})

export const attmUploadgEmits = ['update:fileData', 'update:modelValue']
export type AttmUploadProps = ExtractPropTypes<typeof attmUploadProps>
export type AttmUploadInstance = InstanceType<typeof AttmUpload>
