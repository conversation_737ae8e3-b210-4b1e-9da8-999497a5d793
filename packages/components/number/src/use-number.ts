import { computed, reactive, unref, watch } from 'vue'
import { isNull, isNumber } from 'lodash'
import { pattern } from '@frontend-plus/utils/validator'
import {
  CHANGE_EVENT,
  INPUT_EVENT,
  UPDATE_MODEL_EVENT,
} from '@frontend-plus/constants'
import type { useType } from '@frontend-plus/hooks'

const fullNumberPattern = pattern('fullNumber')
// 正数
const positiveNumberPattern = pattern('positiveNumber')
// 整数
const integerNumberPattern = pattern('integerNumber')
// 正整数
const posIntNumberPattern = pattern('posIntNumber')
// js的number类型整数位所能表达的最大长度
const maxIntegerLength = 13
// 去掉小数点后多余的0
const cancelFillReg = new RegExp(/(?:\.0*|(\.\d+?)0+)$/)

export type NumberState = {
  chanValue: string
  oldInput: string
  maxComputed: string
  minComputed: string
  // 以下属性留作扩展用，例如金额组件
  customState?: {
    precision: number
    symbol: string
  }
}

const RATE_PRECISION = 6

// 获取真正的精度
const getPrecision = (props: any, state: NumberState) => {
  if (state.customState && typeof state.customState.precision !== 'undefined') {
    return state.customState.precision
  }
  if (
    props.isRate &&
    (typeof props.precision === 'undefined' || props.precision === null)
  ) {
    return RATE_PRECISION
  }
  return props.precision
}

// 将值转换为带精度的格式
const _synchInput = (props: any, state: NumberState, val: useType['val']) => {
  if (!val && val !== 0) {
    return ''
  }
  if (typeof val === 'number') {
    val = String(val)
  }

  const precision = getPrecision(props, state)
  const numArr = val.split('.')

  if (precision === 0 && !val.includes('.')) {
    return numArr[0]
  }
  const powNumber =
    numArr.length > 1 && numArr[1] !== ''
      ? 10 ** (precision - numArr[1].length)
      : 10 ** precision
  if (!isNull(precision)) {
    val +=
      val.indexOf('.') > 0
        ? String(powNumber).replace('1', '')
        : String(powNumber).replace('1', '.')
  }
  return val
}

// 将值格式化为千分位格式
export const _formatThousandth = (props: any, val: string): string => {
  const formatVal = isNumber(val) ? String(val) : val
  if (formatVal.indexOf('.') > 0) {
    const arr = formatVal.split('.')
    if (arr[1]) {
      return `${_formatThousandth(props, arr[0])}.${arr[1]}`
    }
  }
  // 可以将千分位符号换成自己定义的
  return formatVal.replace(/\B(?=(\d{3})+(?!\d))/g, props.thousandthSymbol)
}

// 判断输入框的内容是否合法
const _validInput = (val: string, props: any, state: NumberState) => {
  let isValid = false
  if (val === null || val === '') {
    isValid = true
  } else {
    //将输入的中文句号换成引文小数点
    val = val.replace('。', '.')
    if (
      !props.negative &&
      (props.wholeNumber || getPrecision(props, state) === 0)
    ) {
      // 判断是否符合正整数规则
      if (posIntNumberPattern.test(val)) {
        isValid = true
      }
    } else if (!props.negative) {
      // 判断是否符合正数
      if (positiveNumberPattern.test(val)) {
        isValid = true
      } else if (
        val.indexOf('.') === val.length - 1 &&
        positiveNumberPattern.test(val.slice(0, Math.max(0, val.length - 1)))
      ) {
        isValid = true
      }
    } else if (props.wholeNumber || getPrecision(props, state) === 0) {
      // 判断是否符合整数
      if (integerNumberPattern.test(val)) {
        isValid = true
      } else if (val === '-') {
        isValid = true
      }
    } else {
      // 判断是否是合法数字输入
      if (fullNumberPattern.test(val)) {
        isValid = true
      } else if (val === '-') {
        isValid = true
      } else if (
        val.indexOf('.') === val.length - 1 &&
        fullNumberPattern.test(val.slice(0, Math.max(0, val.length - 1)))
      ) {
        isValid = true
      }
    }
  }
  return isValid
}

// 判断是否大于最大值
const _rangeMaxFun = (props: any, state: NumberState) => {
  if (!state.chanValue) {
    return
  }
  if (state.maxComputed) {
    // 整数位和小数位分开来进行比较，防止字符串转数字进行比较的时候出现科学计数法
    const maxArr = state.maxComputed.split('.')
    const numArr = state.chanValue.split('.')
    if (Number(numArr[0]) > Number(maxArr[0])) {
      state.chanValue = unref(state.maxComputed)
      state.oldInput = state.chanValue
    } else if (Number(numArr[0]) === Number(maxArr[0])) {
      const numPrecision =
        numArr.length > 1 && numArr[1] !== '' ? Number(`0.${numArr[1]}`) : 0
      const maxPrecision =
        maxArr.length > 1 && maxArr[1] !== '' ? Number(`0.${maxArr[1]}`) : 0
      // 两个值都为0时，不必继续比较
      if (numPrecision !== 0 || maxPrecision !== 0) {
        let replaceFlag = false
        // 当前值和最大数都为正数时
        if (!numArr[0].includes('-')) {
          replaceFlag = numPrecision > maxPrecision
          // 当前值和最大数都为负数时，小数位大的反而比较小
        } else {
          replaceFlag = numPrecision < maxPrecision
        }
        if (replaceFlag) {
          state.chanValue = String(state.maxComputed)
          state.oldInput = state.chanValue
        }
      }
    }
  }
}

// 判断是否小于最大值
const _rangeMinFun = (props: any, state: NumberState) => {
  if (!state.chanValue) {
    return
  }
  if (state.minComputed) {
    // 整数位和小数位分开来进行比较，防止字符串转数字进行比较的时候出现科学计数法
    const minArr = state.minComputed.split('.')
    const numArr = state.chanValue.split('.')
    if (Number(numArr[0]) < Number(minArr[0])) {
      state.chanValue = unref(state.minComputed)
      state.oldInput = state.chanValue
      return true
    } else if (Number(numArr[0]) === Number(minArr[0])) {
      const numPrecision =
        numArr.length > 1 && numArr[1] !== '' ? Number(`0.${numArr[1]}`) : 0
      const minPrecision =
        minArr.length > 1 && minArr[1] !== '' ? Number(`0.${minArr[1]}`) : 0
      // 两个值都为0时，不必继续比较
      if (numPrecision !== 0 || minPrecision !== 0) {
        let replaceFlag = false
        // 当前值和最大数都为整数时
        if (!numArr[0].includes('-')) {
          replaceFlag = numPrecision < minPrecision
          // 当前值和最大数都为负数时，小数位大的反而比较小
        } else {
          replaceFlag = numPrecision > minPrecision
        }
        if (replaceFlag) {
          state.chanValue = String(state.minComputed)
          state.oldInput = state.chanValue
          return true
        }
      }
    }
  }
}

// 判断是否符合精度要求
const _precisionFun = (
  props: any,
  state: NumberState,
  isUpdateOldValue = true
) => {
  const _precision = getPrecision(props, state)
  if (_precision && state.chanValue !== '') {
    // 取精度时将整数位和小数位拆开来，分别计算以防止出现科学计数法（整数位最高至20位）
    const numArr = state.chanValue.split('.')
    if (numArr.length > 1 && numArr[1].length > _precision) {
      const powNumber = 10 ** _precision
      const precisionNum =
        Math.round(Number(`0.${numArr[1]}`) * powNumber) / powNumber
      if (precisionNum === 0) {
        state.chanValue = numArr[0] + String(powNumber).replace('1', '.')
      } else if (precisionNum === 1) {
        state.chanValue = String(Number(numArr[0]) + 1)
      } else {
        console.log('chanValue', state.chanValue, precisionNum)
        state.chanValue = numArr[0] + String(precisionNum).replace('0.', '.')
      }
      if (isUpdateOldValue) {
        state.oldInput = state.chanValue
      }

      console.log('prec', state.chanValue, state.oldInput)
    }
  }
}

// 由输入框的内容计算出真正需要向外同步的值
const _getEmitValueFun = (props: any, state: NumberState) => {
  const val = state.chanValue
  let emitValue: string | number = ''
  if (val && val !== '-') {
    const _input = val.replace(cancelFillReg, '$1')
    if (props.valueOfString) {
      emitValue = _input
      if (!props.wholeNumber && props.precision && emitValue) {
        emitValue = _synchInput(props, state, emitValue)
      }
    } else {
      emitValue = Number(_input)
    }
  }
  return emitValue
}

// 计算最大、小值，这个方法后续待优化
const computedRange = (props: any) => {
  if (props.valueOfString) {
    return ''
  }
  let _maxIntegerLength = maxIntegerLength
  let decimalPart = 0
  if (props.precision) {
    _maxIntegerLength = _maxIntegerLength - props.precision
    decimalPart = 10 ** props.precision - 1
  }
  const integerPart = 10 ** _maxIntegerLength - 1
  return decimalPart ? integerPart + Number(`0.${decimalPart}`) : integerPart
}

// 判断是否出现了科学计数法
const judgeScientific = (
  props: any,
  state: NumberState,
  value: string | number
) => {
  if (String(value).toLowerCase().includes('e')) {
    state.chanValue = String(props.modelValue)
    state.oldInput = state.chanValue
    return false
  }
  return true
}

export const useState = (props: any) => {
  const maxComputed = computed(() => {
    const max = props.max || props.max === 0 ? props.max : computedRange(props)
    if (typeof max === 'number') {
      return String(max)
    }
    return max
  })

  const minComputed = computed(() => {
    const min =
      props.min || props.min === 0
        ? props.min
        : computedRange(props)
        ? Number(`-${computedRange(props)}`)
        : null
    if (typeof min === 'number') {
      return String(min)
    }
    return min
  })

  return reactive({
    chanValue: '',
    oldInput: '',
    maxComputed,
    minComputed,
  })
}

export const useNumber = (props: any, state: NumberState, emit: any) => {
  let oldModelValue = props.modelValue
  const rangeMaxFun = () => {
    _rangeMaxFun(props, state)
  }
  const rangeMinFun = () => {
    return _rangeMinFun(props, state)
  }
  const precisionFun = () => {
    _precisionFun(props, state)
  }
  const getEmitValueFun = () => {
    return _getEmitValueFun(props, state)
  }

  const parseNumber = (val: string) => {
    state.chanValue = val.replace(cancelFillReg, '$1')
    precisionFun()
  }

  const formatThousandth = (val: string) => {
    return val && props.thousandthSymbol ? _formatThousandth(props, val) : val
  }

  const formatInput = (val: string) => {
    val = _synchInput(props, state, val)
    state.oldInput = val
    state.chanValue = formatThousandth(state.oldInput)
  }

  let isUpdatedByMyself = false
  const handleInput = (val: string) => {
    console.log('handleInput', val)
    if (_validInput(val, props, state)) {
      state.oldInput = val.replace('。', '.')
      state.chanValue = state.oldInput
      rangeMaxFun()
      precisionFun()
      const emitVal = getEmitValueFun()
      if (
        judgeScientific(props, state, emitVal) &&
        emitVal !== props.modelValue
      ) {
        isUpdatedByMyself = true
        emit(UPDATE_MODEL_EVENT, emitVal)
        emit(INPUT_EVENT, emitVal)
      }
    } else {
      state.chanValue = state.oldInput
    }
  }

  const handleChange = () => {
    const rangeMinFlag = rangeMinFun()
    precisionFun()
    const emitVal = getEmitValueFun()
    if (rangeMinFlag) {
      emit(UPDATE_MODEL_EVENT, emitVal, oldModelValue)
    }
    if (
      judgeScientific(props, state, emitVal) &&
      (emitVal !== oldModelValue || rangeMinFlag)
    ) {
      emit(CHANGE_EVENT, emitVal, oldModelValue)
      oldModelValue = emitVal
    }
  }

  const handleFocus = (event: any) => {
    parseNumber(state.oldInput)
    emit('focus', event)
  }

  const handleBlur = (event: any) => {
    formatInput(state.chanValue)
    emit('blur', event)
  }

  watch(
    () => props.modelValue,
    (n, o) => {
      if (isUpdatedByMyself) {
        isUpdatedByMyself = false
      } else {
        if (n !== o && n !== getEmitValueFun()) {
          oldModelValue = n
          formatInput(n)
          _precisionFun(props, state, false)
        }
      }
    },
    {
      immediate: true,
    }
  )

  watch(
    () => props.precision,
    (n, o) => {
      if (n !== o) {
        formatInput(state.oldInput)
      }
    }
  )

  return {
    handleInput,
    handleChange,
    handleFocus,
    handleBlur,
    formatInput,
    formatThousandth,
    getEmitValueFun,
  }
}
