<script lang="ts">
import {
  computed,
  defineComponent,
  h,
  onMounted,
  reactive,
  shallowRef,
} from 'vue'
import { hasOwn } from '@frontend-plus/utils'
import GridWrapper from './grid-wrapper.vue'
import CompactTable from './compact-table.vue'
import { getState, setState } from './state-util'
import { queryGridProps } from './query-grid'
import { useTableData } from './useGrid'
import type { SortOrderMap } from './query-grid'
export default defineComponent({
  name: 'FQueryGrid',
  props: queryGridProps,
  emits: [
    'clear-selection',
    'reset-form',
    'query-table',
    'select',
    'select-all',
    'on-loaded',
    'on-failed',
    'print',
  ],
  setup(props, { emit, slots, attrs, expose }) {
    const sortOrderMap: SortOrderMap = {
      descending: 'desc',
      ascending: 'asc',
    }
    const table = shallowRef()
    const wrapper = shallowRef()
    const {
      tableDataState,
      selectArr,
      renderTableData,
      handleSizeChange,
      handlePageChange,
      getTableData,
      getDataTotal,
      gatherQueryParams,
      tableRowClassName,
    } = useTableData(props, emit)
    const state = reactive({
      isSingle: props.isSingle,
      extendedColumnSchema: [],
    })

    const customState = reactive({
      single: [],
      group: [],
    })

    const customArr = computed<any>(() => {
      return customState[state.isSingle ? 'single' : 'group']
    })

    const doRenderTableData = () => {
      renderTableData({
        pageSize: tableDataState.page.pageSize,
        currentPage: 1,
      })
      state.extendedColumnSchema = props.formData.customFieldDetails || []
    }
    onMounted(() => {
      getState(props.tableCompId).then((res: any) => {
        if (res) {
          customState.single = res.single
          customState.group = res.group
        }
        if (!props.showQueryPanel && props.autoInit) {
          doRenderTableData()
        }
      })
    })

    const handleResetForm = () => {
      emit('reset-form')
    }

    const handleQueryTable = () => {
      if (!props.customQuery) {
        doRenderTableData()
      }
      emit('query-table')
    }

    const handleViewChange = () => {
      state.isSingle = !state.isSingle
    }

    const handleColumnChange = (isSingle: boolean, columns: any) => {
      customState[isSingle ? 'single' : 'group'] = columns
    }

    const handleColumnReset = (isSingle: boolean, columns: any) => {
      customState[isSingle ? 'single' : 'group'] = columns
    }

    const clearSelection = () => {
      getTable().clearSelection()
      selectArr.length = 0
      wrapper.value.updateCountState(getSelectionRows())
      emit('clear-selection')
    }

    const handleStateChange = (
      isSingle: boolean,
      columns: any,
      init: boolean
    ) => {
      customState[isSingle ? 'single' : 'group'] = columns
      if (!init) {
        setState(props.tableCompId, customState)
      }
    }
    /**
     * 清除手动点击的排序的参数
     */
    const clearManulSort = () => {
      tableDataState.manulSort.column.splice(0)
      tableDataState.manulSort.order.splice(0)
    }
    /**
      点击列头排序触发，设置排序参数
     */
    const handleColumnSortChange = (columnSort: any) => {
      clearManulSort()
      if (columnSort.order) {
        tableDataState.manulSort.column.push(columnSort.prop)
        tableDataState.manulSort.order.push(
          sortOrderMap[columnSort.order as keyof SortOrderMap]
        )
      }
    }
    const _formatThousandth = (val: string, needToFixed = true): string => {
      if (val) {
        if (val.indexOf('.') > 0) {
          const arr = val.split('.')
          if (arr[1]) {
            const afterNum =
              arr[1].length < 2 ? `${arr[1]}0` : arr[1].slice(0, 2)
            return `${_formatThousandth(arr[0], false)}.${afterNum}`
          }
        }
        const realVal = needToFixed ? String(Number(val).toFixed(2)) : val
        return realVal.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      return ''
    }
    const checkedSummation = computed(() => {
      const { calcAmountProp, calcLabelProp, calcSymbolProp, calcTotalLabel } =
        props.checkedSummationMap
      const arr = [] as any[]
      selectArr.forEach((check) => {
        const existCalcItem = arr.find((item: any) => {
          return check[calcLabelProp] === item.label
        })
        if (existCalcItem) {
          if (hasOwn(existCalcItem, check[calcSymbolProp])) {
            existCalcItem[check[calcSymbolProp]] += check[calcAmountProp]
          } else {
            existCalcItem[check[calcSymbolProp]] = check[calcAmountProp]
          }
        } else {
          arr.push({
            label: check[calcLabelProp],
            [check[calcSymbolProp]]: Number(check[calcAmountProp]),
          })
        }
      })
      const result = [] as any
      const symbolMap = {} as any
      arr.forEach((r) => {
        const keys = Object.keys(r)
        let _value = ''
        keys.forEach((key) => {
          if (key !== 'label') {
            _value += `${key}${_formatThousandth(String(r[key]))}`
            if (hasOwn(symbolMap, key)) {
              symbolMap[key] += r[key]
            } else {
              symbolMap[key] = r[key]
            }
          }
        })
        result.push({
          label: r.label,
          value: _value,
        })
      })
      if (selectArr.length > 0) {
        result.unshift({
          label: calcTotalLabel,
          value: Object.entries(symbolMap)
            .map(([key, value]) => {
              return `${key}${_formatThousandth(String(value))}`
            })
            .join(''),
        })
      }
      return result
    })

    const handleSelect = (selection: any, row: any) => {
      wrapper.value.updateCountState(getSelectionRows())
      selectArr.length = 0
      selectArr.push(...selection)
      emit('select', selection, row)
    }

    const handleSelectAll = (selection: any) => {
      wrapper.value.updateCountState(getSelectionRows())
      selectArr.length = 0
      selectArr.push(...selection)
      emit('select-all', selection)
    }

    const getSelectionRows = () => {
      return getTable().getSelectionRows()
    }

    const getTable = () => {
      return table.value.table
    }

    const openEdit = (rowKey: any, prop: any) => {
      return table.value.openEdit(rowKey, prop)
    }

    const closeEdit = (rowKey: any, prop: any) => {
      return table.value.closeEdit(rowKey, prop)
    }

    const isEditing = () => {
      return table.value.isEditing()
    }
    const handlePrint = () => {
      emit('print', {
        data: getTableData(),
        total: getDataTotal(),
        state: tableDataState,
      })
    }
    const excuteExport = () => {
      wrapper.value?.excuteExport()
    }
    const getSortParams = () => {
      return wrapper.value?.getSortParams()
    }
    expose({
      renderTableData: doRenderTableData,
      clearSelection,
      getSelectionRows,
      getTableData,
      getDataTotal,
      getTable,
      openEdit,
      closeEdit,
      isEditing,
      excuteExport,
      getSortParams,
    })
    const summaryMethod = (param: any) => {
      if (props.showMultipleSum) {
        const { columns } = param
        const sums: Record<string, any>[][] = []
        tableDataState.sumTotalData.forEach((rowTotal) => {
          const { sumData, totalName } = rowTotal
          const sum: Record<string, any>[] = []
          let firstPosition = -1
          columns.forEach((column: Record<string, any>, cIndex: number) => {
            const _keys = sumData.map((sCol: Record<string, any>) => sCol.prop)
            if (_keys.includes(column.property)) {
              if (firstPosition < 0) {
                firstPosition = cIndex
              }
              const config = sumData.find((data: Record<string, any>) => {
                return data.prop === column.property
              })
              sum.push(config)
            } else {
              sum.push({ value: '' })
            }
          })
          if (totalName && firstPosition > 0) {
            sum[firstPosition - 1] = { value: totalName }
          }
          sums.push(sum)
        })
        return sums
      }
      return []
    }
    const showSummary = props.showMultipleSum ? true : props.showSummary
    return () => {
      const children = {
        default: () =>
          h(CompactTable, {
            ref: table,
            ...attrs,
            isSingle: state.isSingle,
            data: tableDataState.tableData.data,
            customState,
            tableColumns: props.tableColumns,
            allowSort: props.allowSort,
            rowKey: props.rowKey,
            slots,
            scrollbarAlwaysOn: true,
            showSummary,
            extendedColumnSchema: state.extendedColumnSchema,
            showMultipleSum: props.showMultipleSum,
            summaryMethod,
            rowClassName: tableRowClassName,
            onStateChange: handleStateChange,
            onSelect: handleSelect,
            onSelectAll: handleSelectAll,
            onColumnSortChange: handleColumnSortChange,
          }),
      } as any
      if (slots['table-top']) {
        children['table-top'] = slots['table-top']
      }
      if (slots.operate) {
        children.operate = slots.operate
      }
      if (slots['query-panel']) {
        children['query-panel'] = slots['query-panel']
      }
      if (slots['extend-btns']) {
        children['extend-btns'] = slots['extend-btns']
      }
      if (slots['tools']) {
        children['tools'] = slots['tools']
      }
      if (slots['bottom-extend']) {
        children['bottom-extend'] = slots['bottom-extend']
      }
      return h(
        GridWrapper,
        {
          ref: wrapper,
          ...props,
          isSingle: state.isSingle,
          page: tableDataState.page,
          total: tableDataState.tableData.total,
          manulSort: tableDataState.manulSort,
          defaultSort: props.defaultSort,
          sortColumnMap: props.sortColumnMap,
          summation: tableDataState.summation,
          resData: tableDataState.resData,
          customArr: customArr.value,
          customState,
          table,
          autoQuery: props.autoInit,
          formData: props.formData,
          showSummationBiz: props.showSummationBiz,
          showSummationSum: props.showSummationSum,
          showSummation: props.showSummation,
          showCheckedSummation: props.showCheckedSummation,
          checkedSummation: checkedSummation.value,
          rowKey: props.rowKey,
          summationThousandthSymbol: props.summationThousandthSymbol,
          beforeDownload: props.beforeDownload,
          downloadParams: gatherQueryParams,
          onResetForm: handleResetForm,
          onQueryTable: handleQueryTable,
          onPageChange: handlePageChange,
          onSizeChange: handleSizeChange,
          onClearSelection: clearSelection,
          onViewChange: handleViewChange,
          onColumnChange: handleColumnChange,
          onColumnReset: handleColumnReset,
          onPrint: handlePrint,
        },
        children
      )
    }
  },
})
</script>
