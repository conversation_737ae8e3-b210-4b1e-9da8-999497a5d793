import { FormulaUnit, type FormulaUnitType } from './types'
import {
  compareList,
  logicalList,
  valueLabelMap,
  valueToLabel,
} from './constants'

export const deserialize = (input: FormulaUnitType[]) => {
  const result: any[] = []

  input.forEach((item) => {
    if (item.type === FormulaUnit.Number) {
      const num = item.label
        .toString()
        .split('')
        .map((char: string) => {
          if (char === '.') {
            return {
              value: char,
              label: char,
              type: FormulaUnit.Symbol,
            }
          }
          return {
            value: char,
            label: char,
            type: FormulaUnit.Number,
          }
        })
      result.push(...num)
    } else {
      const val = Object.assign({}, item, {
        label: item.label || valueToLabel(item.value),
      })

      result.push(val)
    }
  })

  return result
}

export const serialize = (values: FormulaUnitType[]) => {
  const result: any[] = []
  let lastIsNumber = false
  const tempNumber: string[] = []
  let preValue = ''
  const nextIsNumber = (index: number) => {
    return values[index + 1]?.type === FormulaUnit.Number
  }
  values.forEach((item, index) => {
    if (item.type === FormulaUnit.Number) {
      if (!lastIsNumber) {
        lastIsNumber = true
      }

      tempNumber.push(...item.value)
    } else if (
      item.value === '.' &&
      lastIsNumber &&
      !tempNumber.includes('.') &&
      nextIsNumber(index)
    ) {
      tempNumber.push('.')
    } else {
      if (lastIsNumber) {
        result.push({
          value: Number(tempNumber.join('')),
          label: tempNumber.join(''),
          type: FormulaUnit.Number,
        })
        tempNumber.length = 0
        lastIsNumber = false
      }

      if (
        typeof preValue === 'string' &&
        ['>', '<', '=', '!', '&', '|'].includes(preValue)
      ) {
        const newVal = preValue + item.value
        if (newVal in valueLabelMap) {
          result[result.length - 1].value = newVal
          result[result.length - 1].label = valueLabelMap[newVal]
          if (compareList.includes(newVal)) {
            result[result.length - 1].type = FormulaUnit.Compare
          } else if (logicalList.includes(newVal)) {
            result[result.length - 1].type = FormulaUnit.Logical
          }

          values.splice(index, 1)
          preValue = ''
          return
        }
      }
      result.push(item)
      preValue = item.value
    }
  })

  if (lastIsNumber) {
    result.push({
      value: tempNumber.join(''),
      label: tempNumber.join(''),
      type: FormulaUnit.Number,
    })
    tempNumber.length = 0
    lastIsNumber = false
  }
  return result
}
