import {
  computed,
  defineComponent,
  getCurrentInstance,
  nextTick,
  provide,
  ref,
  renderSlot,
  shallowReactive,
  shallowRef,
  watch,
  inject,
} from 'vue'
import { Plus } from '@dtg/frontend-plus-icons'
import {
  buildProps,
  definePropType,
  isNumber,
  isString,
  isUndefined,
} from '@frontend-plus/utils'
import { EVENT_CODE, UPDATE_MODEL_EVENT } from '@frontend-plus/constants'
import FIcon from '@frontend-plus/components/icon'
import { tabsRootContextKey } from '@frontend-plus/tokens'
import { useDeprecated, useNamespace } from '@frontend-plus/hooks'
import TabNav from './tab-nav'
import { getOrderedPanes } from './utils/pane'

import type { TabNavInstance } from './tab-nav'
import type { TabsPaneContext } from '@frontend-plus/tokens'
import type { ExtractPropTypes } from 'vue'
import type { Awaitable } from '@frontend-plus/utils'

export type TabPaneName = string | number

export const tabsProps = buildProps({
  type: {
    type: String,
    values: ['card', 'border-card', ''],
    default: '',
  },
  activeName: {
    type: [String, Number],
  },
  closable: Boolean,
  addable: Boolean,
  modelValue: {
    type: [String, Number],
  },
  editable: Boolean,
  tabPosition: {
    type: String,
    values: ['top', 'right', 'bottom', 'left'],
    default: 'top',
  },
  beforeLeave: {
    type: definePropType<
      (newName: TabPaneName, oldName: TabPaneName) => Awaitable<void | boolean>
    >(Function),
    default: () => true,
  },
  stretch: Boolean,
  height: {
    type: String,
  },
  isAddIcon: {
    type: Boolean,
    default: true,
  },
  tabCloseable: {
    type: Boolean,
    default: true,
  },
} as const)
export type TabsProps = ExtractPropTypes<typeof tabsProps>

const isPaneName = (value: unknown): value is string | number =>
  isString(value) || isNumber(value)

export const tabsEmits = {
  [UPDATE_MODEL_EVENT]: (name: TabPaneName) => isPaneName(name),
  tabClick: (pane: TabsPaneContext, ev: Event) => ev instanceof Event,
  tabChange: (name: TabPaneName) => isPaneName(name),
  edit: (paneName: TabPaneName | undefined, action: 'remove' | 'add') =>
    ['remove', 'add'].includes(action),
  tabRemove: (name: TabPaneName) => isPaneName(name),
  tabAdd: () => true,
}
export type TabsEmits = typeof tabsEmits

export type TabsPanes = Record<number, TabsPaneContext>

export default defineComponent({
  name: 'FTabs',

  props: tabsProps,
  emits: tabsEmits,

  setup(props, { emit, slots, expose }) {
    const vm = getCurrentInstance()!

    const ns = useNamespace('tabs')

    const nav$ = ref<TabNavInstance>()
    const panes = shallowReactive<TabsPanes>({})
    const orderedPanes = shallowRef<TabsPaneContext[]>([])
    const currentName = ref<TabPaneName>(
      props.modelValue ?? props.activeName ?? '0'
    )

    const changeCurrentName = (value: TabPaneName) => {
      currentName.value = value
      emit(UPDATE_MODEL_EVENT, value)
      emit('tabChange', value)
    }

    const setCurrentName = async (value?: TabPaneName) => {
      // should do nothing.
      if (currentName.value === value || isUndefined(value)) return

      try {
        const canLeave = await props.beforeLeave?.(value, currentName.value)
        if (canLeave !== false) {
          changeCurrentName(value)

          // call exposed function, Vue doesn't support expose in typescript yet.
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          nav$.value?.removeFocus?.()
        }
      } catch {}
    }

    const handleTabClick = (
      tab: TabsPaneContext,
      tabName: TabPaneName,
      event: Event
    ) => {
      if (tab.props.disabled) return
      setCurrentName(tabName)
      emit('tabClick', tab, event)
    }

    const handleTabRemove = (pane: TabsPaneContext, ev: Event) => {
      if (pane.props.disabled || isUndefined(pane.props.name)) return
      ev.stopPropagation()
      emit('edit', pane.props.name, 'remove')
      emit('tabRemove', pane.props.name)
    }

    const handleTabAdd = () => {
      emit('edit', undefined, 'add')
      emit('tabAdd')
    }

    useDeprecated(
      {
        from: '"activeName"',
        replacement: '"model-value" or "v-model"',
        scope: 'FTabs',
        version: '2.3.0',
        ref: 'https://element-plus.org/en-US/component/tabs.html#attributes',
        type: 'Attribute',
      },
      computed(() => !!props.activeName)
    )

    watch(
      () => props.activeName,
      (modelValue) => setCurrentName(modelValue)
    )

    // 为了实现不同tab刷新底下锚点，通过最外层组件注入的刷新锚点方法在每次tab变得时候刷新一下
    const blanckSceneContext = inject<any>('getAnchorList', undefined)

    watch(
      () => props.modelValue,
      (modelValue) => setCurrentName(modelValue)
    )

    watch(currentName, async () => {
      await nextTick()
      if (blanckSceneContext) {
        blanckSceneContext?.refrshPointOnTabs()
      }
      // call exposed function, Vue doesn't support expose in typescript yet.
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      nav$.value?.scrollToActiveTab()
    })

    {
      const registerPane = (pane: TabsPaneContext) => {
        panes[pane.uid] = pane
        orderedPanes.value = getOrderedPanes(vm, panes)
      }

      const unregisterPane = (uid: number) => {
        delete panes[uid]
        orderedPanes.value = getOrderedPanes(vm, panes)
      }

      provide(tabsRootContextKey, {
        props,
        currentName,
        registerPane,
        unregisterPane,
      })
    }

    expose({
      currentName,
      setCurrentName,
      orderedPanes,
    })

    return () => {
      const newButton =
        (props.editable && props.isAddIcon) ||
        (props.addable && props.isAddIcon) ? (
          <span
            class={ns.e('new-tab')}
            tabindex="0"
            onClick={handleTabAdd}
            onKeydown={(ev: KeyboardEvent) => {
              if (ev.code === EVENT_CODE.enter) handleTabAdd()
            }}
          >
            <FIcon class={ns.is('icon-plus')}>
              <Plus />
            </FIcon>
          </span>
        ) : null
      const headerSlot = (
        <div class={ns.e('content-header')}>{renderSlot(slots, 'header')}</div>
      )

      const header = (
        <div
          style={{ height: props.height }}
          class={[ns.e('header'), ns.is(props.tabPosition)]}
        >
          {newButton}
          <TabNav
            ref={nav$}
            currentName={currentName.value}
            editable={props.editable}
            type={props.type}
            isAddIcon={props.isAddIcon}
            panes={orderedPanes.value}
            stretch={props.stretch}
            tabCloseable={props.tabCloseable}
            onTabClick={handleTabClick}
            onTabRemove={handleTabRemove}
            onChangeAdd={handleTabAdd}
          >
            {headerSlot}
          </TabNav>
        </div>
      )

      const panels = (
        <div class={ns.e('content')}>{renderSlot(slots, 'default')}</div>
      )

      return (
        <div
          class={[
            ns.b(),
            ns.m(props.tabPosition),
            {
              [ns.m('card')]: props.type === 'card',
              [ns.m('border-card')]: props.type === 'border-card',
            },
          ]}
        >
          {...props.tabPosition !== 'bottom'
            ? [header, panels]
            : [panels, header]}
        </div>
      )
    }
  },
})
