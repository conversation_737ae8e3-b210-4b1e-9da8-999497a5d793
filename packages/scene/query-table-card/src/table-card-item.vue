<template>
  <div ref="tableCardColl" :class="ns.b()">
    <f-collapse-item>
      <template #title>
        <div :class="ns.e('header')">
          <div
            v-for="rank in state.realHeaderColumns"
            :key="rank.name"
            :class="ns.e('column')"
            :style="{
              width: rank.type !== 'check' ? state.averageWidth + 'px' : 'auto',
            }"
          >
            <slot :name="rank.name">
              <template v-if="rank.type !== 'currencySymbol'">
                <f-table-header-item
                  v-bind="$attrs"
                  :item="rank"
                  :label-map="state.headerLabelProp"
                  :disabled-check="state.disabledCheck"
                  :source="data"
                  :name-key="nameKey"
                  :show-check="showCheck"
                  :currency-symbol="getCurrencySymbol()"
                  @header-button-click="handleClickLink"
                />
              </template>
            </slot>
          </div>
        </div>
      </template>
      <f-detail-table v-bind="$attrs" :data-source="state.columns" />
      <div v-if="state.hidProcess" :class="ns.e('process')">
        <f-button type="primary" plain @click="toProcess(data)"
          >流程跟踪
        </f-button>
      </div>
    </f-collapse-item>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useEventListener, useThrottleFn } from '@vueuse/core'
import { isArray, isNaN, isString } from 'lodash'
import { useNamespace } from '@frontend-plus/hooks'
import { FButton, FCollapseItem } from '@frontend-plus/components'
import { isBoolean, isFunction } from '@frontend-plus/utils'
import { tableCardItemProps } from './table-card-item'
import FTableHeaderItem from './table-header-item.vue'
import FDetailTable from './detail-table.vue'

defineOptions({
  name: 'FTableCardItem',
})
const props = defineProps(tableCardItemProps)
const emits = defineEmits(['to-process', 'to-detail'])
const ns = useNamespace('table-card-item')
const state = reactive({
  realHeaderColumns: [] as any[],
  headerLabelProp: props.setHeaderLabelMap
    ? props.setHeaderLabelMap(props.data)
    : {},
  tableData: {} as object,
  columns: {} as any[],
  averageWidth: 0 as number,
  disabledCheck: props.setCheckBoxDisabled
    ? props.setCheckBoxDisabled(props.data)
    : false,
  hidProcess: isBoolean(props.setProcessShow)
    ? props.setProcessShow
    : isFunction(props.setProcessShow)
    ? props.setProcessShow(props.data)
    : false,
})

const getCurrencySymbol = () => {
  if (props.data.currencySymbol) {
    return props.data.currencySymbol
  }
  // 获取表头 右侧金额的币种符号
  let symbol = '￥'
  if (state.realHeaderColumns && isArray(state.realHeaderColumns)) {
    for (const headerConfig of state.realHeaderColumns) {
      if (headerConfig.type === 'currencySymbol') {
        symbol = state.headerLabelProp[headerConfig.field]
        break
      }
    }
  }
  return symbol
}

const tableCardColl = ref()
const handleClickLink = () => {
  emits('to-detail', props.data)
}
const calcViewport = useThrottleFn(() => {
  const headerWidth = Number.parseInt(
    getComputedStyle(tableCardColl.value).width
  )
  let headerColumWidth = 0
  state.realHeaderColumns.forEach((res) => {
    if (res.type !== 'check') {
      headerColumWidth++
    }
  })
  state.averageWidth = (headerWidth - 80) / headerColumWidth
}, 10)

useEventListener(window, 'resize', calcViewport)

onMounted(() => {
  calcViewport()
  renderTable()
})
if (props.setHeaderColumns) {
  state.realHeaderColumns = props.setHeaderColumns(props.data)
}

// 千分位分隔符
function formatNumber(input: unknown, decimalPlaces = 2) {
  // 处理空值
  if (input === null || input === undefined || input === '') {
    return ''
  }

  // 转换为字符串并清理
  let str = String(input).trim()

  // 移除已有的千分位逗号
  str = str.replace(/,/g, '')

  // 尝试转换为数字
  const num = Number.parseFloat(str)

  // 处理无效数字
  if (Number.isNaN(num)) {
    return 'NaN'
  }

  // 处理无限大
  if (!Number.isFinite(num)) {
    return num.toString()
  }

  // 确保小数位数为非负整数
  decimalPlaces = Math.max(0, Math.floor(decimalPlaces))

  // 格式化为固定小数位数（自动四舍五入和补零）
  const fixedNum = num.toFixed(decimalPlaces)

  // 分割整数和小数部分
  const parts = fixedNum.split('.')
  let integerPart = parts[0]
  let decimalPart = parts.length > 1 ? parts[1] : ''

  // 补全小数部分的零
  if (decimalPlaces > 0) {
    decimalPart = decimalPart.padEnd(decimalPlaces, '0')
  }

  // 添加千分位分隔符
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 组合结果
  return decimalPlaces > 0 ? `${integerPart}.${decimalPart}` : integerPart
}

const doFormatAmount = (val: string | number, precision = 2) => {
  const _val = isString(val) ? val : String(val)
  return formatNumber(_val, precision)
}

const AMOUNTWORD = 'AMOUNT'

/**
 * 渲染单元格显示内容
 */
const renderCellText = (
  data: Record<string, any>[],
  target: Record<string, any>
) => {
  const _res = data.find((i) => {
    return i.field === target.field
  })
  const value = _res ? _res.value : ''
  // 处理金额/利率类型的值
  if (target.fieldShowType) {
    // 金额
    if (target.fieldShowType === 'amount') {
      const tmp = doFormatAmount(value)
      if (isNaN(tmp) || tmp === 'NaN') {
        return value
      } else {
        return tmp
      }
    } else if (target.fieldShowType === 'rate') {
      // 利率
      const tmp = doFormatAmount(value, 6)
      if (isNaN(tmp) || tmp === 'NaN') {
        return value
      } else {
        return tmp
      }
    } else {
      // 处理常规字段的逻辑，保留判断单词中又AMOUNT的情况
      if (target.field.toUpperCase().includes(AMOUNTWORD)) {
        const tmp = doFormatAmount(value)
        if (isNaN(tmp) || tmp === 'NaN') {
          return value
        } else {
          return tmp
        }
      }
      return value
    }
  } else {
    // 防止接口没有返回filedShowType,保留之前判断是否是金额的逻辑，避免大量没有设置金额类型的字段没有格式化
    if (target.field.toUpperCase().includes(AMOUNTWORD)) {
      const tmp = doFormatAmount(value)
      if (isNaN(tmp) || tmp === 'NaN') {
        return value
      } else {
        return tmp
      }
    }
    return value
  }
}

const renderTable = () => {
  const columns = { columns: null, data: [] } as any
  const row = {} as any
  columns.columns = props.data.columns
  props.data.columnDes.forEach((item: any) => {
    columns.data.push({
      field: item.field,
      title: item.label,
      params: {
        source: props.data.source,
      },
      value: renderCellText(props.data.data, item),
      span: item.span,
    })
    row[item.field] = item.value
  })
  state.columns = columns
  state.tableData = row
}

// 流程跟踪
const toProcess = (data: any) => {
  emits('to-process', data)
}
</script>
