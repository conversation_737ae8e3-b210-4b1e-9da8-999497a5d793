<template>
  <div :id="sceneId" :class="ns.b()">
    <div ref="wapperRef" :class="ns.b('warpper')">
      <div v-if="pageType !== 'view'" ref="headerRef" :class="ns.e('header')">
        <slot name="blank-header">
          <f-scene-status :title="title" :show-help="showHelp" />
        </slot>
      </div>
      <div :class="ns.e('content')">
        <div ref="contentheader" :class="ns.e('content-header')">
          <slot name="content-header" />
        </div>
        <f-scrollbar
          :height="height"
          :prevent-jitter="true"
          @scroll="handleScroll"
        >
          <slot />
        </f-scrollbar>
      </div>
      <div
        v-if="pageType === 'add' && showFooter"
        ref="footerRef"
        :class="ns.e('footer')"
      >
        <div :class="ns.e('left')">
          <div :class="ns.e('step')">
            <f-point
              v-if="
                isPoint &&
                state.scenePointList &&
                state.scenePointList.length > 1
              "
              :scene-point-list="state.scenePointList"
              :point-list="state.pointList"
              :anchor-index="state.currentAnchor"
            />
          </div>
        </div>
        <div style="display: flex">
          <div :id="teleId" :class="ns.e('middle')">
            <f-button v-if="showPreviewButton"
              ><f-icon color="#0052D9"><dtg-doc-preview /></f-icon
              >{{ t('el.elDocPreview') }}</f-button
            >
            <slot name="footer" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  getCurrentInstance,
  nextTick,
  onMounted,
  provide,
  reactive,
  ref,
  watch,
} from 'vue'
import { DtgDocPreview } from '@dtg/frontend-plus-icons'
import {
  useEventListener,
  useResizeObserver,
  useThrottleFn,
} from '@vueuse/core'
import { FButton, FIcon, FScrollbar } from '@frontend-plus/components'
import { useLocale, useNamespace } from '@frontend-plus/hooks'
import { randomId, sortPoint } from '@frontend-plus/utils'
import { FSceneStatus } from '../../scene-status'
import { FPoint } from '../../point'
import { blankSceneProps } from './blank-scene'

const ns = useNamespace('blank-scene')
const { t } = useLocale()

defineOptions({
  name: 'FBlankScene',
})

const props = defineProps(blankSceneProps)
const emit = defineEmits(['get-height', 'scroll'])

const height = ref<number | string>(0)
const footerRef = ref()
const headerRef = ref()
const wapperRef = ref()
const contentheader = ref()

const state = reactive({
  pointList: [] as any[],
  currentAnchor: 0, // 多页签当前下标
  scenePointList: [] as any[], // 渲染锚地列表
  lastStep: 0,
  blankParentId: '',
  paneId: [],
})
const instance = getCurrentInstance()
const heightDelta = 10

onMounted(() => {
  getHeight()
  useResizeObserver(contentheader.value, calcViewport)
  nextTick(() => {
    emit('get-height', height.value)
  })
})

// tab场景下锚点是动态得，判断当前panel是否渲染
const isElementVisible = (el: HTMLElement) => {
  if (!el) return false // 元素不存在
  const style = window.getComputedStyle(el)
  return (
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0' &&
    el.offsetWidth > 0 &&
    el.offsetHeight > 0
  )
}

const refrshPointOnTabs = () => {
  if (state.pointList.length > 0) {
    state.scenePointList = state.pointList.filter((element, index, self) => {
      const el = element?.currentTempleteRef
      const isVisible = isElementVisible(el)
      return (
        isVisible &&
        self?.findIndex(
          (x) =>
            (x.id ? x.id === element.id : x.name === element.name) &&
            x.currentTempleteRef !== null
        ) === index
      )
    })
    state.scenePointList = sortPoint(state.scenePointList)
  }
}

watch(
  () => state.pointList,
  () => {
    refrshPointOnTabs()
  },
  { deep: true }
)

provide('getAnchorList', {
  getAnchorList: (val: any): void => {
    state.pointList.push(val)
  },
  refrshPointOnTabs,
})

// 页面滚动锚点高亮
const handleScroll = ({
  scrollTop,
}: {
  scrollLeft: number
  scrollTop: number
}) => {
  for (let i = 0; i < state.scenePointList.length; i++) {
    if (scrollTop >= state.scenePointList[i].currentTempleteRef.offsetTop) {
      state.currentAnchor = i
    }
  }
  emit('scroll', { scrollTop })
}

const calcViewport = useThrottleFn(() => {
  // 内容区域高度
  const wapperHeigh = Number.parseInt(getComputedStyle(wapperRef.value).height)
  // Footer高度
  if (props.pageType === 'add') {
    const headerHeight = Number.parseInt(
      getComputedStyle(headerRef.value).height
    )
    const footerHeight = Number.parseInt(
      getComputedStyle(footerRef.value).height
    )
    const _contentHeader = contentheader.value
      ? Number.parseInt(getComputedStyle(contentheader.value).height)
      : 0
    height.value = `${
      wapperHeigh -
      (footerHeight + headerHeight + heightDelta + 10 + _contentHeader)
    }px`
  }
  emit('get-height', height.value)
}, 10)

useEventListener(window, 'resize', calcViewport)
const getHeight = () => {
  // 内容区域高度
  const wapperHeigh = Number.parseInt(getComputedStyle(wapperRef.value).height)
  // Footer高度
  if (props.pageType === 'add' && props.showFooter) {
    const footerHeight = Number.parseInt(
      getComputedStyle(footerRef.value).height
    )
    const headerHeight = Number.parseInt(
      getComputedStyle(headerRef.value).height
    )
    const _contentHeader = contentheader.value
      ? Number.parseInt(getComputedStyle(contentheader.value).height)
      : 0
    height.value = `${
      wapperHeigh -
      (footerHeight + headerHeight + heightDelta + 10 + _contentHeader)
    }px`
  } else {
    if (instance?.attrs.class === 'FApprovalScene') {
      height.value = `${wapperHeigh + 60}px`
    } else {
      const footerHeight = 50
      const headerHeight = 50
      height.value = `${
        wapperHeigh - (footerHeight + headerHeight + heightDelta)
      }px`
    }
  }
}

const teleId = props.teleportId || randomId()
const sceneId = props.blankSceneId || randomId()
provide('fBlankScene', { sceneId })
defineExpose({ state })
</script>
